#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AEC-FT 项目全面清理脚本
基于代码审计报告的清理建议
"""

import os
import shutil
import glob
from datetime import datetime

def create_backup():
    """创建备份目录"""
    backup_dir = f"backup/code_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    print(f"📦 创建备份目录: {backup_dir}")
    return backup_dir

def cleanup_temp_reports():
    """清理临时报告文件"""
    print("🗑️ 清理临时报告文件...")
    
    # 临时报告文件模式
    temp_patterns = [
        "connection_pool_health_report_*.json",
        "connection_pool_leak_fix_summary.md",
        "connection_pool_diagnosis_and_fix.md", 
        "PowerShell构建问题解决方案.md",
        "Redis和Psutil使用情况分析及精简优化方案.md",
        "COMPREHENSIVE_SCHEDULING_FIX_PLAN.md",
        "快速验证结果总结.md",
        "数据连接池*.md"
    ]
    
    deleted_count = 0
    total_size = 0
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                os.remove(file_path)
                print(f"   ✅ 删除: {file_path} ({size} bytes)")
                deleted_count += 1
                total_size += size
    
    return deleted_count, total_size

def cleanup_test_files():
    """清理测试文件"""
    print("🧪 清理测试文件...")
    
    test_files = [
        "test_connection_pool_comprehensive.py",
        "test_connection_pool_quick_verify.py", 
        "test_data_connection_pool_complete.py",
        "check_connection_pool_health.py",
        "fix_connection_pool_leaks.py",
        "scheduling_failure_fix.py"
    ]
    
    deleted_count = 0
    total_size = 0
    
    for file_path in test_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            os.remove(file_path)
            print(f"   ✅ 删除: {file_path} ({size} bytes)")
            deleted_count += 1
            total_size += size
    
    return deleted_count, total_size

def cleanup_pycache():
    """清理Python缓存文件"""
    print("🐍 清理Python缓存文件...")
    
    deleted_count = 0
    total_size = 0
    
    # 清理__pycache__目录
    for root, dirs, files in os.walk("."):
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            try:
                # 计算目录大小
                for dirpath, dirnames, filenames in os.walk(pycache_path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                        deleted_count += 1
                
                shutil.rmtree(pycache_path)
                print(f"   ✅ 删除目录: {pycache_path}")
            except Exception as e:
                print(f"   ⚠️ 无法删除 {pycache_path}: {e}")
    
    return deleted_count, total_size

def cleanup_log_files():
    """清理过期日志文件"""
    print("📝 清理过期日志文件...")
    
    deleted_count = 0
    total_size = 0
    
    # 清理轮转的日志文件（保留最新的）
    log_patterns = [
        "logs/*.log.*",  # 轮转的日志文件
        "logs/app20*.log"  # 旧的应用日志
    ]
    
    for pattern in log_patterns:
        files = glob.glob(pattern)
        for file_path in files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                os.remove(file_path)
                print(f"   ✅ 删除: {file_path} ({size} bytes)")
                deleted_count += 1
                total_size += size
    
    return deleted_count, total_size

def cleanup_build_artifacts():
    """清理构建产物"""
    print("🔨 清理构建产物...")
    
    deleted_count = 0
    total_size = 0
    
    # 清理spec文件
    spec_files = glob.glob("*.spec")
    for file_path in spec_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            os.remove(file_path)
            print(f"   ✅ 删除: {file_path} ({size} bytes)")
            deleted_count += 1
            total_size += size
    
    return deleted_count, total_size

def verify_system():
    """验证系统完整性"""
    print("✅ 验证系统完整性...")
    
    try:
        # 检查关键文件是否存在
        critical_files = [
            "run.py",
            "app/__init__.py",
            "config/aps_config.py",
            "requirements.txt"
        ]
        
        for file_path in critical_files:
            if not os.path.exists(file_path):
                print(f"   ❌ 关键文件缺失: {file_path}")
                return False
        
        # 尝试导入应用
        import sys
        sys.path.insert(0, '.')
        try:
            import app
            print("   ✅ 应用导入成功")
            return True
        except Exception as e:
            print(f"   ❌ 应用导入失败: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ 系统验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始AEC-FT项目全面清理...")
    print("=" * 50)
    
    # 创建备份
    backup_dir = create_backup()
    
    total_deleted = 0
    total_size = 0
    
    # 执行各种清理
    cleanup_functions = [
        cleanup_temp_reports,
        cleanup_test_files,
        cleanup_pycache,
        cleanup_log_files,
        cleanup_build_artifacts
    ]
    
    for cleanup_func in cleanup_functions:
        try:
            deleted, size = cleanup_func()
            total_deleted += deleted
            total_size += size
        except Exception as e:
            print(f"   ⚠️ 清理过程中出现错误: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 清理完成!")
    print(f"   删除文件数: {total_deleted}")
    print(f"   释放空间: {total_size / 1024:.1f} KB")
    print(f"   备份位置: {backup_dir}")
    
    # 验证系统
    if verify_system():
        print("✅ 系统验证通过，清理成功！")
        return True
    else:
        print("❌ 系统验证失败，请检查备份并恢复！")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n⚠️ 如果系统出现问题，请从备份恢复：")
        print("   cp -r backup/code_cleanup_*/* ./")
