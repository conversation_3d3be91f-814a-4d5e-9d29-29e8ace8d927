"""
订单数据管理API
支持FT订单和CP订单的现代化数据管理功能
"""

from flask import Blueprint, request, jsonify, send_file
from flask_login import login_required
import logging
from datetime import datetime, timedelta
import os
import tempfile
import pandas as pd
# 已迁移到Flask-SQLAlchemy，不再需要get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

order_data_bp = Blueprint('order_data', __name__)

def success_response(data, pagination=None, statistics=None):
    """成功响应格式"""
    response = {
        'success': True,
        'data': data
    }
    if pagination:
        response['pagination'] = pagination
    if statistics:
        response['statistics'] = statistics
    return jsonify(response)

def error_response(message):
    """错误响应格式"""
    return jsonify({
        'success': False,
        'message': message
    }), 400

@order_data_bp.route('/ft-summary', methods=['GET'])
@login_required
def get_ft_summary():
    """获取FT订单汇总数据"""
    try:
        page = int(request.args.get('page', 1))
        page_size = request.args.get('page_size', '50')
        
        # 处理显示全部的情况
        if page_size == 'all':
            page_size = 1000000
        else:
            page_size = int(page_size)
        
        # 筛选参数
        field = request.args.get('field', '')
        operator = request.args.get('operator', 'contains')
        value = request.args.get('value', '')
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建基础查询
            base_query = "SELECT * FROM ft_order_summary"
            where_conditions = []
            params = []
            
            # 应用筛选条件
            if field and value:
                if operator == 'contains':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}%')
                elif operator == 'equals':
                    where_conditions.append(f"{field} = %s")
                    params.append(value)
                elif operator == 'starts_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'{value}%')
                elif operator == 'ends_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}')
            
            # 构建完整查询
            if where_conditions:
                base_query += " WHERE " + " AND ".join(where_conditions)
            
            # 计算总数
            count_query = f"SELECT COUNT(*) as count FROM ({base_query}) as filtered"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['count']
            
            # 添加排序和分页
            base_query += " ORDER BY id DESC"
            offset = (page - 1) * page_size
            paginated_query = f"{base_query} LIMIT %s OFFSET %s"
            params.extend([page_size, offset])
            
            # 执行分页查询
            cursor.execute(paginated_query, params)
            results = cursor.fetchall()
            
            # 获取列名
            cursor.execute("DESCRIBE ft_order_summary")
            columns_info = cursor.fetchall()
            column_names = [col['Field'] for col in columns_info]
            
            # 转换为字典格式 (DictCursor已经返回字典)
            data = []
            for row in results:
                row_dict = {}
                for column_name, value in row.items():
                    if isinstance(value, datetime):
                        row_dict[column_name] = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif hasattr(value, 'strftime'):  # 处理日期类型
                        row_dict[column_name] = value.strftime('%Y-%m-%d')
                    else:
                        row_dict[column_name] = value
                data.append(row_dict)
            
            # 计算统计信息
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary")
            total_records = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%工程%'")
            engineering_count = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM ft_order_summary WHERE classification_result LIKE '%量产%'")
            production_count = cursor.fetchone()['count']
            
            # 分页信息
            total_pages = (total_count + page_size - 1) // page_size
            pagination = {
                'current_page': page,
                'total_pages': total_pages,
                'total': total_count,
                'start': offset + 1 if total_count > 0 else 0,
                'end': min(offset + page_size, total_count)
            }
            
            # 统计信息
            statistics = {
                'total': total_records,
                'valid': total_records,  # 假设所有记录都是有效的
                'engineering': engineering_count,
                'production': production_count
            }
            
            return success_response(data, pagination, statistics)
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"获取FT订单数据失败: {str(e)}")
        return error_response(f"获取FT订单数据失败: {str(e)}")

@order_data_bp.route('/cp-summary', methods=['GET'])
@login_required
def get_cp_summary():
    """获取CP订单汇总数据"""
    try:
        page = int(request.args.get('page', 1))
        page_size = request.args.get('page_size', '50')
        
        # 处理显示全部的情况
        if page_size == 'all':
            page_size = 1000000
        else:
            page_size = int(page_size)
        
        # 筛选参数
        field = request.args.get('field', '')
        operator = request.args.get('operator', 'contains')
        value = request.args.get('value', '')
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建基础查询
            base_query = "SELECT * FROM cp_order_summary"
            where_conditions = []
            params = []
            
            # 应用筛选条件
            if field and value:
                if operator == 'contains':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}%')
                elif operator == 'equals':
                    where_conditions.append(f"{field} = %s")
                    params.append(value)
                elif operator == 'starts_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'{value}%')
                elif operator == 'ends_with':
                    where_conditions.append(f"{field} LIKE %s")
                    params.append(f'%{value}')
            
            # 构建完整查询
            if where_conditions:
                base_query += " WHERE " + " AND ".join(where_conditions)
            
            # 计算总数
            count_query = f"SELECT COUNT(*) as count FROM ({base_query}) as filtered"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['count']
            
            # 添加排序和分页
            base_query += " ORDER BY id DESC"
            offset = (page - 1) * page_size
            paginated_query = f"{base_query} LIMIT %s OFFSET %s"
            params.extend([page_size, offset])
            
            # 执行分页查询
            cursor.execute(paginated_query, params)
            results = cursor.fetchall()
            
            # 获取列名
            cursor.execute("DESCRIBE cp_order_summary")
            columns_info = cursor.fetchall()
            column_names = [col['Field'] for col in columns_info]
            
            # 转换为字典格式 (DictCursor已经返回字典)
            data = []
            for row in results:
                row_dict = {}
                for column_name, value in row.items():
                    if isinstance(value, datetime):
                        row_dict[column_name] = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif hasattr(value, 'strftime'):  # 处理日期类型
                        row_dict[column_name] = value.strftime('%Y-%m-%d')
                    else:
                        row_dict[column_name] = value
                data.append(row_dict)
            
            # 计算统计信息
            cursor.execute("SELECT COUNT(*) as count FROM cp_order_summary")
            total_records = cursor.fetchone()['count']
            
            # CP订单可能没有工程/量产分类，使用其他统计
            engineering_count = 0
            production_count = total_records
            
            # 分页信息
            total_pages = (total_count + page_size - 1) // page_size
            pagination = {
                'current_page': page,
                'total_pages': total_pages,
                'total': total_count,
                'start': offset + 1 if total_count > 0 else 0,
                'end': min(offset + page_size, total_count)
            }
            
            # 统计信息
            statistics = {
                'total': total_records,
                'valid': total_records,
                'engineering': engineering_count,
                'production': production_count
            }
            
            return success_response(data, pagination, statistics)
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"获取CP订单数据失败: {str(e)}")
        return error_response(f"获取CP订单数据失败: {str(e)}")

@order_data_bp.route('/ft-summary/export', methods=['GET'])
@login_required
def export_ft_summary():
    """导出FT订单数据"""
    try:
        export_format = request.args.get('format', 'excel')
        ids = request.args.get('ids', '')
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建查询
            if ids:
                id_list = ids.split(',')
                placeholders = ','.join(['%s'] * len(id_list))
                query = f"SELECT * FROM ft_order_summary WHERE id IN ({placeholders})"
                cursor.execute(query, id_list)
            else:
                cursor.execute("SELECT * FROM ft_order_summary")
            
            results = cursor.fetchall()
            
            # 获取列名
            cursor.execute("DESCRIBE ft_order_summary")
            columns_info = cursor.fetchall()
            column_names = [col['Field'] for col in columns_info]
            
            # 创建DataFrame
            df = pd.DataFrame(results, columns=column_names)
            
            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            if export_format == 'csv':
                file_path = os.path.join(temp_dir, 'ft_orders.csv')
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                mimetype = 'text/csv'
            else:
                file_path = os.path.join(temp_dir, 'ft_orders.xlsx')
                df.to_excel(file_path, index=False)
                mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            return send_file(
                file_path,
                as_attachment=True,
                download_name=f'ft_orders_{datetime.now().strftime("%Y%m%d_%H%M%S")}.{export_format}',
                mimetype=mimetype
            )
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"导出FT订单数据失败: {str(e)}")
        return error_response(f"导出失败: {str(e)}")

@order_data_bp.route('/cp-summary/export', methods=['GET'])
@login_required
def export_cp_summary():
    """导出CP订单数据"""
    try:
        export_format = request.args.get('format', 'excel')
        ids = request.args.get('ids', '')
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 构建查询
            if ids:
                id_list = ids.split(',')
                placeholders = ','.join(['%s'] * len(id_list))
                query = f"SELECT * FROM cp_order_summary WHERE id IN ({placeholders})"
                cursor.execute(query, id_list)
            else:
                cursor.execute("SELECT * FROM cp_order_summary")
            
            results = cursor.fetchall()
            
            # 获取列名
            cursor.execute("DESCRIBE cp_order_summary")
            columns_info = cursor.fetchall()
            column_names = [col['Field'] for col in columns_info]
            
            # 创建DataFrame
            df = pd.DataFrame(results, columns=column_names)
            
            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            if export_format == 'csv':
                file_path = os.path.join(temp_dir, 'cp_orders.csv')
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                mimetype = 'text/csv'
            else:
                file_path = os.path.join(temp_dir, 'cp_orders.xlsx')
                df.to_excel(file_path, index=False)
                mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            return send_file(
                file_path,
                as_attachment=True,
                download_name=f'cp_orders_{datetime.now().strftime("%Y%m%d_%H%M%S")}.{export_format}',
                mimetype=mimetype
            )
            
        finally:
            cursor.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"导出CP订单数据失败: {str(e)}")
        return error_response(f"导出失败: {str(e)}")
