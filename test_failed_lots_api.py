#!/usr/bin/env python3
"""
测试失败批次API接口
模拟前端调用，检查API响应
"""

import requests
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API基础URL
BASE_URL = "http://localhost:5000"

def test_failed_lots_api():
    """测试失败批次API"""
    try:
        # 1. 测试当前失败批次API
        logger.info("🔍 测试当前失败批次API...")
        current_url = f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=true"
        
        response = requests.get(current_url, timeout=30)
        logger.info(f"📡 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ API调用成功")
            logger.info(f"📊 响应数据结构: {list(data.keys())}")
            
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_count = data.get('data', {}).get('total_count', 0)
                summary = data.get('data', {}).get('summary', {})
                
                logger.info(f"📈 失败批次总数: {total_count}")
                logger.info(f"📊 统计信息: {summary}")
                
                if failed_lots:
                    logger.info(f"📋 前5条失败批次:")
                    for i, lot in enumerate(failed_lots[:5], 1):
                        logger.info(f"  {i}. LOT_ID: {lot.get('LOT_ID', 'N/A')}")
                        logger.info(f"     DEVICE: {lot.get('DEVICE', 'N/A')}")
                        logger.info(f"     STAGE: {lot.get('STAGE', 'N/A')}")
                        logger.info(f"     FAILURE_REASON: {lot.get('failure_reason', 'N/A')}")
                        logger.info(f"     TIMESTAMP: {lot.get('timestamp', 'N/A')}")
                else:
                    logger.warning("⚠️ API返回的失败批次列表为空")
            else:
                logger.error(f"❌ API返回失败: {data.get('message', '未知错误')}")
        else:
            logger.error(f"❌ API调用失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 网络请求失败: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON解析失败: {e}")
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {e}")

def test_historical_failed_lots_api():
    """测试历史失败批次API"""
    try:
        logger.info("🔍 测试历史失败批次API...")
        history_url = f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=false"
        
        response = requests.get(history_url, timeout=30)
        logger.info(f"📡 历史API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_count = data.get('data', {}).get('total_count', 0)
                logger.info(f"📈 历史失败批次总数: {total_count}")
                
                if total_count > 0:
                    logger.info("✅ 历史API正常返回数据")
                else:
                    logger.warning("⚠️ 历史API返回空数据")
            else:
                logger.error(f"❌ 历史API返回失败: {data.get('message', '未知错误')}")
        else:
            logger.error(f"❌ 历史API调用失败，状态码: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ 历史API测试失败: {e}")

def test_filter_options_api():
    """测试筛选选项API"""
    try:
        logger.info("🔍 测试筛选选项API...")
        filter_url = f"{BASE_URL}/api/v2/production/get-failed-lots-filter-options"
        
        response = requests.get(filter_url, timeout=30)
        logger.info(f"📡 筛选选项API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                logger.info("✅ 筛选选项API正常")
                logger.info(f"📊 筛选选项: {data.get('data', {})}")
            else:
                logger.error(f"❌ 筛选选项API返回失败: {data.get('message', '未知错误')}")
        else:
            logger.error(f"❌ 筛选选项API调用失败，状态码: {response.status_code}")
            
    except Exception as e:
        logger.error(f"❌ 筛选选项API测试失败: {e}")

def check_server_status():
    """检查服务器状态"""
    try:
        logger.info("🔍 检查服务器状态...")
        health_url = f"{BASE_URL}/"
        
        response = requests.get(health_url, timeout=10)
        if response.status_code == 200:
            logger.info("✅ 服务器运行正常")
            return True
        else:
            logger.error(f"❌ 服务器状态异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 无法连接到服务器: {e}")
        return False

def main():
    """主函数"""
    logger.info("🧪 开始测试失败批次API接口...")
    
    # 1. 检查服务器状态
    if not check_server_status():
        logger.error("❌ 服务器不可用，请先启动应用")
        return
    
    # 2. 测试当前失败批次API
    test_failed_lots_api()
    
    print("-" * 50)
    
    # 3. 测试历史失败批次API
    test_historical_failed_lots_api()
    
    print("-" * 50)
    
    # 4. 测试筛选选项API
    test_filter_options_api()
    
    logger.info("🏁 API测试完成")

if __name__ == "__main__":
    main()
