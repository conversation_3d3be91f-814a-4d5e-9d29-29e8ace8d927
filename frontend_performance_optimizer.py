#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端性能优化脚本
立即执行的性能优化措施
"""

import os
import shutil
import json
from datetime import datetime

def analyze_static_resources():
    """分析静态资源使用情况"""
    print("🔍 分析静态资源...")
    
    static_dir = "app/static"
    resource_analysis = {
        'duplicates': [],
        'large_files': [],
        'unused_files': [],
        'total_size': 0
    }
    
    # 检查重复文件
    file_hashes = {}
    for root, dirs, files in os.walk(static_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                file_size = os.path.getsize(file_path)
                resource_analysis['total_size'] += file_size
                
                # 检查大文件 (>500KB)
                if file_size > 500 * 1024:
                    resource_analysis['large_files'].append({
                        'path': file_path,
                        'size_kb': round(file_size / 1024, 1)
                    })
                
                # 检查重复文件名
                if file in file_hashes:
                    resource_analysis['duplicates'].append({
                        'filename': file,
                        'paths': [file_hashes[file], file_path],
                        'sizes': [os.path.getsize(file_hashes[file]), file_size]
                    })
                else:
                    file_hashes[file] = file_path
                    
            except Exception as e:
                print(f"   ⚠️ 无法分析文件 {file_path}: {e}")
    
    return resource_analysis

def remove_duplicate_resources():
    """删除重复的静态资源"""
    print("🗑️ 删除重复资源...")
    
    # 已知的重复文件
    duplicates_to_remove = [
        "app/static/xlsx.full.min.js",  # 根目录的旧版本
        "app/static/vendor/bootstrap/bootstrap.min.css.backup",  # 备份文件
    ]
    
    removed_count = 0
    saved_space = 0
    
    for file_path in duplicates_to_remove:
        if os.path.exists(file_path):
            try:
                file_size = os.path.getsize(file_path)
                os.remove(file_path)
                print(f"   ✅ 删除重复文件: {file_path} ({file_size / 1024:.1f}KB)")
                removed_count += 1
                saved_space += file_size
            except Exception as e:
                print(f"   ❌ 删除失败 {file_path}: {e}")
    
    return removed_count, saved_space

def optimize_resource_loading():
    """优化资源加载配置"""
    print("⚡ 优化资源加载...")
    
    # 创建优化的资源配置
    optimized_config = {
        "lazy_load": {
            "echarts": {
                "condition": "chart_needed",
                "path": "vendor/echarts/echarts.min.js",
                "size_kb": 1000
            },
            "xlsx": {
                "condition": "export_needed", 
                "path": "vendor/xlsx/xlsx.full.min.js",
                "size_kb": 900
            },
            "fullcalendar": {
                "condition": "calendar_needed",
                "path": "vendor/fullcalendar/main.min.js",
                "size_kb": 200
            }
        },
        "preload": [
            "vendor/bootstrap/bootstrap.bundle.min.js",
            "js/unified_api_client.js",
            "js/menu-optimizer.js"
        ],
        "defer": [
            "js/ai_assistant.js",
            "js/backend_scheduled_tasks.js"
        ]
    }
    
    # 保存配置
    config_path = "app/static/resource_optimization.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 资源优化配置已保存: {config_path}")
    return config_path

def create_smart_refresh_js():
    """创建智能刷新JavaScript"""
    print("🧠 创建智能刷新脚本...")
    
    smart_refresh_js = '''/**
 * 智能刷新管理器 - 解决频繁轮询导致的性能问题
 */
class SmartRefreshManager {
    constructor() {
        this.intervals = {
            active: 30000,      // 用户活跃时30秒
            inactive: 120000,   // 用户不活跃时2分钟  
            background: 300000, // 页面在后台时5分钟
            offline: 600000     // 离线时10分钟
        };
        
        this.state = {
            isActive: true,
            isVisible: true,
            isOnline: navigator.onLine,
            lastActivity: Date.now()
        };
        
        this.refreshCallbacks = new Map();
        this.currentTimer = null;
        
        this.init();
    }
    
    init() {
        this.setupActivityListeners();
        this.setupVisibilityListener();
        this.setupOnlineListener();
        this.startRefreshCycle();
        
        console.log('🚀 智能刷新管理器已启动');
    }
    
    setupActivityListeners() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.recordActivity();
            }, { passive: true });
        });
        
        // 检查用户不活跃状态
        setInterval(() => {
            const inactiveTime = Date.now() - this.state.lastActivity;
            this.state.isActive = inactiveTime < 60000; // 1分钟内有活动
        }, 10000);
    }
    
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            this.state.isVisible = !document.hidden;
            this.updateRefreshInterval();
            
            if (this.state.isVisible) {
                // 页面重新可见时立即刷新一次
                this.executeRefresh();
            }
        });
    }
    
    setupOnlineListener() {
        window.addEventListener('online', () => {
            this.state.isOnline = true;
            this.updateRefreshInterval();
            this.executeRefresh(); // 重新上线时立即刷新
        });
        
        window.addEventListener('offline', () => {
            this.state.isOnline = false;
            this.updateRefreshInterval();
        });
    }
    
    recordActivity() {
        this.state.lastActivity = Date.now();
        if (!this.state.isActive) {
            this.state.isActive = true;
            this.updateRefreshInterval();
        }
    }
    
    updateRefreshInterval() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
        }
        
        let interval = this.getOptimalInterval();
        
        this.currentTimer = setInterval(() => {
            this.executeRefresh();
        }, interval);
        
        console.log(`🔄 刷新间隔已更新: ${interval/1000}秒`);
    }
    
    getOptimalInterval() {
        if (!this.state.isOnline) {
            return this.intervals.offline;
        }
        
        if (!this.state.isVisible) {
            return this.intervals.background;
        }
        
        return this.state.isActive ? this.intervals.active : this.intervals.inactive;
    }
    
    registerRefreshCallback(name, callback) {
        this.refreshCallbacks.set(name, callback);
        console.log(`📝 注册刷新回调: ${name}`);
    }
    
    unregisterRefreshCallback(name) {
        this.refreshCallbacks.delete(name);
        console.log(`🗑️ 移除刷新回调: ${name}`);
    }
    
    async executeRefresh() {
        if (!this.state.isOnline) {
            console.log('📡 离线状态，跳过刷新');
            return;
        }
        
        console.log('🔄 执行智能刷新...');
        
        for (const [name, callback] of this.refreshCallbacks) {
            try {
                await callback();
            } catch (error) {
                console.error(`❌ 刷新回调失败 ${name}:`, error);
            }
        }
    }
    
    startRefreshCycle() {
        this.updateRefreshInterval();
    }
    
    destroy() {
        if (this.currentTimer) {
            clearInterval(this.currentTimer);
        }
        this.refreshCallbacks.clear();
        console.log('🛑 智能刷新管理器已销毁');
    }
}

// 全局智能刷新管理器
window.smartRefresh = new SmartRefreshManager();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.smartRefresh) {
        window.smartRefresh.destroy();
    }
});

// 替换原有的定时刷新
if (typeof loadStats === 'function') {
    window.smartRefresh.registerRefreshCallback('dashboard_stats', loadStats);
}

if (typeof updateCharts === 'function') {
    window.smartRefresh.registerRefreshCallback('dashboard_charts', updateCharts);
}
'''
    
    js_path = "app/static/js/smart-refresh.js"
    with open(js_path, 'w', encoding='utf-8') as f:
        f.write(smart_refresh_js)
    
    print(f"   ✅ 智能刷新脚本已创建: {js_path}")
    return js_path

def create_memory_manager_js():
    """创建内存管理JavaScript"""
    print("🧠 创建内存管理脚本...")
    
    memory_manager_js = '''/**
 * 内存管理器 - 防止内存泄漏
 */
class MemoryManager {
    constructor() {
        this.timers = new Map();
        this.intervals = new Map();
        this.listeners = new Map();
        this.observers = new Set();
        
        this.init();
    }
    
    init() {
        this.setupCleanupListeners();
        this.startMemoryMonitoring();
        console.log('🧠 内存管理器已启动');
    }
    
    // 定时器管理
    setTimeout(callback, delay, id = null) {
        const timerId = id || `timer_${Date.now()}_${Math.random()}`;
        
        if (this.timers.has(timerId)) {
            clearTimeout(this.timers.get(timerId));
        }
        
        const timer = setTimeout(() => {
            callback();
            this.timers.delete(timerId);
        }, delay);
        
        this.timers.set(timerId, timer);
        return timerId;
    }
    
    setInterval(callback, delay, id = null) {
        const intervalId = id || `interval_${Date.now()}_${Math.random()}`;
        
        if (this.intervals.has(intervalId)) {
            clearInterval(this.intervals.get(intervalId));
        }
        
        const interval = setInterval(callback, delay);
        this.intervals.set(intervalId, interval);
        return intervalId;
    }
    
    clearTimeout(id) {
        if (this.timers.has(id)) {
            clearTimeout(this.timers.get(id));
            this.timers.delete(id);
        }
    }
    
    clearInterval(id) {
        if (this.intervals.has(id)) {
            clearInterval(this.intervals.get(id));
            this.intervals.delete(id);
        }
    }
    
    // 事件监听器管理
    addEventListener(element, event, handler, options = {}) {
        const key = `${element.id || 'anonymous'}_${event}_${Date.now()}`;
        
        element.addEventListener(event, handler, options);
        this.listeners.set(key, { element, event, handler, options });
        
        return key;
    }
    
    removeEventListener(key) {
        const listener = this.listeners.get(key);
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler);
            this.listeners.delete(key);
        }
    }
    
    // Observer管理
    addObserver(observer) {
        this.observers.add(observer);
        return observer;
    }
    
    removeObserver(observer) {
        if (observer && typeof observer.disconnect === 'function') {
            observer.disconnect();
        }
        this.observers.delete(observer);
    }
    
    // 内存监控
    startMemoryMonitoring() {
        if (!performance.memory) {
            console.warn('⚠️ 浏览器不支持内存监控');
            return;
        }
        
        const baseline = performance.memory.usedJSHeapSize;
        
        this.setInterval(() => {
            const current = performance.memory.usedJSHeapSize;
            const increase = current - baseline;
            const increaseMB = increase / 1024 / 1024;
            
            if (increaseMB > 50) { // 50MB增长警告
                console.warn(`⚠️ 内存使用增长: ${increaseMB.toFixed(1)}MB`);
                this.suggestCleanup();
            }
        }, 30000, 'memory_monitor');
    }
    
    suggestCleanup() {
        console.log('🧹 建议执行内存清理...');
        
        // 清理API缓存
        if (window.apiClient && typeof window.apiClient.clearCache === 'function') {
            window.apiClient.clearCache();
        }
        
        // 清理过期的定时器
        this.cleanupExpiredTimers();
    }
    
    cleanupExpiredTimers() {
        // 清理可能泄漏的定时器
        let cleaned = 0;
        
        this.timers.forEach((timer, id) => {
            if (id.includes('temp_') || id.includes('auto_')) {
                clearTimeout(timer);
                this.timers.delete(id);
                cleaned++;
            }
        });
        
        if (cleaned > 0) {
            console.log(`🧹 清理了 ${cleaned} 个临时定时器`);
        }
    }
    
    setupCleanupListeners() {
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // 页面隐藏时部分清理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.partialCleanup();
            }
        });
    }
    
    partialCleanup() {
        // 清理非关键资源
        this.cleanupExpiredTimers();
    }
    
    cleanup() {
        console.log('🧹 执行内存清理...');
        
        // 清理所有定时器
        this.timers.forEach(timer => clearTimeout(timer));
        this.intervals.forEach(interval => clearInterval(interval));
        
        // 清理事件监听器
        this.listeners.forEach(listener => {
            listener.element.removeEventListener(listener.event, listener.handler);
        });
        
        // 清理观察器
        this.observers.forEach(observer => {
            if (observer && typeof observer.disconnect === 'function') {
                observer.disconnect();
            }
        });
        
        // 清空集合
        this.timers.clear();
        this.intervals.clear();
        this.listeners.clear();
        this.observers.clear();
        
        console.log('✅ 内存清理完成');
    }
    
    getStats() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            listeners: this.listeners.size,
            observers: this.observers.size,
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null
        };
    }
}

// 全局内存管理器
window.memoryManager = new MemoryManager();

// 替换原生方法
window.managedSetTimeout = (callback, delay, id) => window.memoryManager.setTimeout(callback, delay, id);
window.managedSetInterval = (callback, delay, id) => window.memoryManager.setInterval(callback, delay, id);
window.managedAddEventListener = (element, event, handler, options) => window.memoryManager.addEventListener(element, event, handler, options);
'''
    
    js_path = "app/static/js/memory-manager.js"
    with open(js_path, 'w', encoding='utf-8') as f:
        f.write(memory_manager_js)
    
    print(f"   ✅ 内存管理脚本已创建: {js_path}")
    return js_path

def update_base_template():
    """更新基础模板以包含性能优化脚本"""
    print("📝 更新基础模板...")
    
    base_template_path = "app/templates/base.html"
    
    # 备份原文件
    backup_path = f"{base_template_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(base_template_path, backup_path)
    print(f"   📦 已备份原模板: {backup_path}")
    
    # 添加性能优化脚本引用
    performance_scripts = '''
    <!-- 性能优化脚本 -->
    <script src="{{ url_for('static', filename='js/memory-manager.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/smart-refresh.js') }}" defer></script>
    '''
    
    print(f"   ✅ 建议在base.html中添加性能优化脚本")
    print(f"   📋 请手动添加以下代码到<head>标签中:")
    print(performance_scripts)
    
    return backup_path

def main():
    """主函数"""
    print("🚀 开始前端性能优化...")
    print("=" * 50)
    
    # 1. 分析静态资源
    analysis = analyze_static_resources()
    print(f"📊 静态资源分析完成:")
    print(f"   总大小: {analysis['total_size'] / 1024 / 1024:.1f}MB")
    print(f"   大文件: {len(analysis['large_files'])}个")
    print(f"   重复文件: {len(analysis['duplicates'])}组")
    
    # 2. 删除重复资源
    removed_count, saved_space = remove_duplicate_resources()
    print(f"   删除文件: {removed_count}个")
    print(f"   节省空间: {saved_space / 1024:.1f}KB")
    
    # 3. 优化资源加载
    config_path = optimize_resource_loading()
    
    # 4. 创建智能刷新脚本
    smart_refresh_path = create_smart_refresh_js()
    
    # 5. 创建内存管理脚本
    memory_manager_path = create_memory_manager_js()
    
    # 6. 更新基础模板
    backup_path = update_base_template()
    
    print("\n" + "=" * 50)
    print("🎉 前端性能优化完成!")
    print(f"📁 创建的文件:")
    print(f"   - {config_path}")
    print(f"   - {smart_refresh_path}")
    print(f"   - {memory_manager_path}")
    print(f"📦 备份文件: {backup_path}")
    
    print("\n📋 下一步操作:")
    print("1. 手动更新base.html模板添加性能脚本")
    print("2. 测试页面加载性能")
    print("3. 监控内存使用情况")
    print("4. 根据需要调整刷新间隔")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 优化脚本执行成功！")
    else:
        print("\n❌ 优化过程中出现问题，请检查日志。")
