<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>失败批次测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .loading { background-color: #fff3cd; color: #856404; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>失败批次功能测试</h1>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="testCurrentFailedLots()">测试当前失败批次</button>
            <button class="btn btn-secondary" onclick="testHistoryFailedLots()">测试历史失败批次</button>
            <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="status" class="status info">点击按钮开始测试...</div>
        
        <div id="summary" style="margin: 20px 0;"></div>
        
        <div id="results"></div>
    </div>

    <script>
        let testResults = [];
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateSummary(data) {
            const summaryDiv = document.getElementById('summary');
            if (data && data.summary) {
                summaryDiv.innerHTML = `
                    <h3>统计信息</h3>
                    <p><strong>总失败数:</strong> ${data.summary.total_failed}</p>
                    <p><strong>配置缺失:</strong> ${data.summary.config_missing}</p>
                    <p><strong>设备不兼容:</strong> ${data.summary.equipment_incompatible}</p>
                    <p><strong>其他原因:</strong> ${data.summary.other_reasons}</p>
                `;
            }
        }
        
        function displayResults(lots) {
            const resultsDiv = document.getElementById('results');
            
            if (!lots || lots.length === 0) {
                resultsDiv.innerHTML = '<p class="error">没有找到失败批次数据</p>';
                return;
            }
            
            let tableHtml = `
                <h3>失败批次列表 (共 ${lots.length} 条)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>批次ID</th>
                            <th>产品名称</th>
                            <th>工序</th>
                            <th>失败原因</th>
                            <th>时间戳</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            lots.slice(0, 20).forEach((lot, index) => {
                tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${lot.LOT_ID || 'N/A'}</td>
                        <td>${lot.DEVICE || 'N/A'}</td>
                        <td>${lot.STAGE || 'N/A'}</td>
                        <td>${lot.failure_reason || 'N/A'}</td>
                        <td>${lot.timestamp || 'N/A'}</td>
                    </tr>
                `;
            });
            
            tableHtml += '</tbody></table>';
            
            if (lots.length > 20) {
                tableHtml += `<p class="info">只显示前20条记录，总共${lots.length}条</p>`;
            }
            
            resultsDiv.innerHTML = tableHtml;
        }
        
        async function testCurrentFailedLots() {
            try {
                updateStatus('正在加载当前失败批次...', 'loading');
                
                const url = '/api/v2/production/get-failed-lots-from-logs?current_only=true';
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('API响应:', result);
                
                if (result.success) {
                    const failedLots = result.data.failed_lots || [];
                    updateStatus(`成功加载 ${failedLots.length} 条当前失败批次`, 'success');
                    updateSummary(result.data);
                    displayResults(failedLots);
                    testResults = failedLots;
                } else {
                    throw new Error(result.message || '获取失败批次数据失败');
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                updateStatus(`测试失败: ${error.message}`, 'error');
                document.getElementById('results').innerHTML = `<p class="error">错误详情: ${error.message}</p>`;
            }
        }
        
        async function testHistoryFailedLots() {
            try {
                updateStatus('正在加载历史失败批次...', 'loading');
                
                const url = '/api/v2/production/get-failed-lots-from-logs?current_only=false';
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('API响应:', result);
                
                if (result.success) {
                    const failedLots = result.data.failed_lots || [];
                    updateStatus(`成功加载 ${failedLots.length} 条历史失败批次`, 'success');
                    updateSummary(result.data);
                    displayResults(failedLots);
                    testResults = failedLots;
                } else {
                    throw new Error(result.message || '获取失败批次数据失败');
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                updateStatus(`测试失败: ${error.message}`, 'error');
                document.getElementById('results').innerHTML = `<p class="error">错误详情: ${error.message}</p>`;
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('summary').innerHTML = '';
            updateStatus('结果已清空', 'info');
            testResults = [];
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载');
            updateStatus('页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
