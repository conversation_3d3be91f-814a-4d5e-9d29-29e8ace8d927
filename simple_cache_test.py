#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的缓存测试脚本
验证静态资源缓存是否生效
"""

import requests
import time

def test_static_resource_cache():
    """测试静态资源缓存"""
    print("🔍 测试静态资源缓存效果...")
    
    base_url = "http://localhost:5000"
    
    # 测试静态资源
    static_resources = [
        "/static/vendor/bootstrap/bootstrap.min.css",
        "/static/js/unified_api_client.js",
        "/static/js/quick-navigation.js"
    ]
    
    session = requests.Session()
    
    for resource in static_resources:
        url = f"{base_url}{resource}"
        print(f"\n📁 测试: {resource}")
        
        # 第一次请求
        print("   第一次请求...")
        start_time = time.time()
        try:
            response1 = session.get(url, timeout=10)
            first_time = (time.time() - start_time) * 1000
            
            print(f"   ✅ 状态码: {response1.status_code}")
            print(f"   ⏱️ 响应时间: {first_time:.1f}ms")
            print(f"   📦 内容大小: {len(response1.content) / 1024:.1f}KB")
            print(f"   🏷️ 缓存头部: {response1.headers.get('Cache-Control', 'None')}")
            print(f"   🗜️ 压缩: {response1.headers.get('Content-Encoding', 'None')}")
            
            # 第二次请求（应该从缓存获取）
            print("   第二次请求...")
            start_time = time.time()
            response2 = session.get(url, timeout=10)
            second_time = (time.time() - start_time) * 1000
            
            print(f"   ✅ 状态码: {response2.status_code}")
            print(f"   ⏱️ 响应时间: {second_time:.1f}ms")
            print(f"   🏷️ 缓存头部: {response2.headers.get('Cache-Control', 'None')}")
            
            # 分析缓存效果
            if second_time < first_time * 0.8:
                improvement = ((first_time - second_time) / first_time * 100)
                print(f"   🎉 缓存生效! 提升 {improvement:.0f}%")
            else:
                print(f"   ⚠️ 缓存可能未生效")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def test_page_load():
    """测试页面加载"""
    print("\n🌐 测试页面加载...")
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    # 登录
    try:
        login_response = session.post(f"{base_url}/auth/login", data={
            "username": "admin",
            "password": "admin"
        }, timeout=10)
        
        if login_response.status_code == 200:
            print("   ✅ 登录成功")
        else:
            print("   ⚠️ 登录可能失败，继续测试...")
    except:
        print("   ⚠️ 登录失败，继续测试...")
    
    # 测试主页
    print("   测试主页加载...")
    start_time = time.time()
    try:
        response = session.get(f"{base_url}/", timeout=10)
        load_time = (time.time() - start_time) * 1000
        
        print(f"   ✅ 状态码: {response.status_code}")
        print(f"   ⏱️ 加载时间: {load_time:.1f}ms")
        print(f"   📦 页面大小: {len(response.content) / 1024:.1f}KB")
        print(f"   🏷️ 缓存头部: {response.headers.get('Cache-Control', 'None')}")
        print(f"   🗜️ 压缩: {response.headers.get('Content-Encoding', 'None')}")
        
        if load_time < 1000:
            print("   🎉 页面加载速度优秀!")
        elif load_time < 2000:
            print("   👍 页面加载速度良好")
        else:
            print("   ⚠️ 页面加载较慢，需要优化")
            
    except Exception as e:
        print(f"   ❌ 页面加载失败: {e}")

def main():
    """主函数"""
    print("🧪 简单缓存和性能测试")
    print("=" * 50)
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ 服务器正在运行")
    except:
        print("❌ 服务器未运行，请先启动: python run.py")
        return
    
    # 测试静态资源缓存
    test_static_resource_cache()
    
    # 测试页面加载
    test_page_load()
    
    print("\n" + "=" * 50)
    print("🎯 测试建议:")
    print("1. 如果缓存未生效，检查浏览器开发者工具的Network面板")
    print("2. 如果页面加载慢，可能是服务器性能问题")
    print("3. 在浏览器中测试菜单切换，观察Network面板的缓存命中情况")

if __name__ == "__main__":
    main()
