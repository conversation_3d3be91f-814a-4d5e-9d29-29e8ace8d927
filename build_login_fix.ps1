# APS Platform - Login Fix Build Script
# Version: 4.0 - 自动版本命名和优化打包

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "APS Platform - 自动版本打包脚本 v4.0" -ForegroundColor Cyan
Write-Host "自动读取版本号并生成带版本的exe文件" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

# 读取版本号
Write-Host "读取应用版本信息..." -ForegroundColor Yellow
$configPath = "config\__init__.py"
if (Test-Path $configPath) {
    $configContent = Get-Content $configPath -Raw
    if ($configContent -match "APP_VERSION\s*=\s*['\`"]([^'\`"]+)['\`"]") {
        $appVersion = $matches[1]
        Write-Host "✅ 应用版本: $appVersion" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 警告: 未找到版本信息，使用默认版本 2.3.4" -ForegroundColor Yellow
        $appVersion = "2.3.4"
    }
} else {
    Write-Host "⚠️ 警告: 配置文件不存在，使用默认版本 2.3.4" -ForegroundColor Yellow
    $appVersion = "2.3.4"
}

# 生成带版本号的exe名称
$exeName = "AEC-FT-Intelligent-Commander-Platform-$appVersion-Production"
Write-Host "📦 目标文件名: $exeName.exe (生产级版本)" -ForegroundColor Cyan

# Check environment
Write-Host "检查构建环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Python" -ForegroundColor Red
    exit 1
}

# Clean old files
Write-Host "清理旧构建文件..." -ForegroundColor Yellow
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
Get-ChildItem -Path "." -Filter "*.spec" | Remove-Item -Force

# Verify model architecture works
Write-Host "验证重构后的模型架构..." -ForegroundColor Cyan
$modelTest = 'import os; os.environ["FLASK_QUIET_STARTUP"] = "1"; from app.models import User, ET_WAIT_LOT; print("✅ 重构后的模型正常工作!")'
echo $modelTest | python
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 模型验证失败" -ForegroundColor Red
    exit 1
}

# Verify critical files exist
Write-Host "验证关键文件..." -ForegroundColor Yellow
$criticalFiles = @(
    "app/models.py",
    "app/templates/auth/login.html",
    "app/templates/base.html"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ 缺失: $file" -ForegroundColor Red
        exit 1
    }
}

# Execute PyInstaller with complete file collection
Write-Host "开始构建带版本号的可执行文件..." -ForegroundColor Yellow
Write-Host "包含模板文件和数据库模型以支持登录功能" -ForegroundColor Cyan

# 检查MySQL客户端库
Write-Host "检查MySQL依赖..." -ForegroundColor Yellow
$mysqlLibPath = "C:\Program Files\MySQL\MySQL Server 8.0\lib\libmysql.dll"
$mysqlBinaryArgs = @()
if (Test-Path $mysqlLibPath) {
    Write-Host "✅ 找到MySQL客户端库: $mysqlLibPath" -ForegroundColor Green
    $mysqlBinaryArgs = @("--add-binary", "$mysqlLibPath;.")
} else {
    Write-Host "⚠️ 未找到MySQL客户端库，将使用PyMySQL替代" -ForegroundColor Yellow
}

# 构建PyInstaller参数数组
$pyinstallerArgs = @(
    "run.py",
    "--name", "$exeName",
    "--icon", "icon/icon.ico",
    "--clean",
    "--noconfirm", 
    "--onefile",
    "--console",
    # 核心依赖
    "--hidden-import", "pymysql",
    "--hidden-import", "sqlalchemy.dialects.mysql",
    "--hidden-import", "flask",
    "--hidden-import", "flask_sqlalchemy",
    "--hidden-import", "flask_login",
    "--hidden-import", "app.models",
    "--hidden-import", "app.auth",
    "--hidden-import", "app.api_v2",
    "--hidden-import", "app.services",
    "--hidden-import", "waitress",
    "--hidden-import", "scheduling_failure_fix",
    "--hidden-import", "tools.monitoring.scheduling_failure_fix",
    # 排除大型不必要的库
    "--exclude-module", "torch",
    "--exclude-module", "torchvision", 
    "--exclude-module", "torchaudio",
    "--exclude-module", "scipy",
    "--exclude-module", "matplotlib",
    "--exclude-module", "cv2",
    "--exclude-module", "ultralytics",
    "--exclude-module", "IPython",
    "--exclude-module", "jupyter",
    "--exclude-module", "notebook",
    "--exclude-module", "psycopg2",
    "--exclude-module", "oracledb",
    "--exclude-module", "cx_Oracle",
    "--exclude-module", "tensorflow",
    "--exclude-module", "keras",
    "--exclude-module", "sklearn",
    "--exclude-module", "seaborn",
    "--exclude-module", "plotly",
    "--exclude-module", "bokeh",
    "--exclude-module", "dash",
    "--exclude-module", "streamlit",
    # 收集应用模块
    "--collect-submodules", "app",
    # 添加数据文件
    "--add-data", "app/templates;app/templates",
    "--add-data", "app/static;app/static", 
    "--add-data", "app/models.py;app",
    "--add-data", "config;config"
)

# 添加MySQL二进制文件（如果存在）
if ($mysqlBinaryArgs.Count -gt 0) {
    $pyinstallerArgs += $mysqlBinaryArgs
}

Write-Host "🎯 生产级优化策略:" -ForegroundColor Green
Write-Host "- 排除深度学习库 (torch, tensorflow等)" -ForegroundColor Green
Write-Host "- 排除科学计算库 (scipy, matplotlib等)" -ForegroundColor Green
Write-Host "- 包含Waitress生产服务器" -ForegroundColor Green
Write-Host "- 排除开发工具 (IPython, jupyter等)" -ForegroundColor Green
Write-Host "- 排除不必要的数据库驱动" -ForegroundColor Green
Write-Host "- 专注于核心业务功能" -ForegroundColor Green

python -m PyInstaller @pyinstallerArgs

# Check result
if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "========================================================" -ForegroundColor Green
    Write-Host "✅ 构建成功!" -ForegroundColor Green
    Write-Host "========================================================" -ForegroundColor Green
    
    $finalExePath = "dist/$exeName.exe"
    if (Test-Path $finalExePath) {
        $size = (Get-Item $finalExePath).Length
        $sizeMB = [math]::Round($size / 1MB, 1)
        
        Write-Host ""
        Write-Host "📁 可执行文件: $finalExePath" -ForegroundColor Yellow
        Write-Host "📊 文件大小: $sizeMB MB" -ForegroundColor Cyan
        Write-Host "🏷️ 应用版本: $appVersion" -ForegroundColor Magenta
        Write-Host ""
        Write-Host "🎯 构建摘要:" -ForegroundColor Green
        Write-Host "- ✅ 包含所有模板文件 (app/templates)" -ForegroundColor Green
        Write-Host "- ✅ 包含静态文件 (app/static)" -ForegroundColor Green
        Write-Host "- ✅ 显式包含模型文件 (models.py)" -ForegroundColor Green
        Write-Host "- ✅ 包含配置文件 (config)" -ForegroundColor Green
        Write-Host "- ✅ 登录功能应该正常工作" -ForegroundColor Green
        Write-Host "- ✅ 自动版本命名: $exeName" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 部署说明:" -ForegroundColor Cyan
        Write-Host "1. 复制 $exeName.exe 到目标机器" -ForegroundColor White
        Write-Host "2. 创建 config/ 文件夹并配置 environment.ini" -ForegroundColor White
        Write-Host "3. 在 config/environment.ini 中配置数据库连接" -ForegroundColor White
        Write-Host "4. 运行exe文件" -ForegroundColor White
        
# 创建配置文件模板（只在不存在时创建，避免覆盖用户配置）
        if (-not (Test-Path "dist/config.ini")) {
            $configTemplate = @"
# AEC-FT Database Configuration File
# Please modify the following configuration according to your environment

[DATABASE]
# MySQL database connection configuration
host = localhost
port = 3306
database = aps
username = root
password = WWWwww123!
charset = utf8mb4

[APPLICATION]
# Application configuration
debug = false
host = 0.0.0.0
port = 5000
secret_key = your-secret-key-here

[LOGGING]
# Logging configuration
level = INFO
file = logs/app.log
max_size = 10MB
backup_count = 5
"@
            # 使用ASCII编码避免BOM问题
            [System.IO.File]::WriteAllText("dist/config.ini", $configTemplate, [System.Text.Encoding]::ASCII)
            Write-Host "📄 已生成配置文件模板: dist/config.ini (ASCII编码)" -ForegroundColor Green
        } else {
            Write-Host "📄 保留现有配置文件: dist/config.ini" -ForegroundColor Yellow
        }

        # 创建数据库初始化脚本 (英文版本 - 避免编码问题)
        $dbInitScript = @"
@echo off
chcp 65001 >nul
title AEC-FT Database Configuration Wizard

echo.
echo ================================================
echo    AEC-FT Database Configuration Wizard
echo ================================================
echo.
echo This wizard will help you configure database connection
echo.

REM Check if config.ini exists
if not exist "config.ini" (
    echo ERROR: Cannot find config.ini file
    echo Please make sure this script is in the same directory as the exe file
    echo.
    pause
    exit /b 1
)

echo Please make sure MySQL service is running
echo.

REM Get database configuration
set /p db_host="Enter MySQL server address [default: localhost]: "
if "%db_host%"=="" set db_host=localhost

set /p db_port="Enter MySQL port [default: 3306]: "
if "%db_port%"=="" set db_port=3306

set /p db_user="Enter MySQL username [default: root]: "
if "%db_user%"=="" set db_user=root

set /p db_password="Enter MySQL password: "
if "%db_password%"=="" (
    echo.
    echo ERROR: MySQL password cannot be empty
    echo Please run this wizard again and enter the correct password
    echo.
    pause
    exit /b 1
)

set /p db_name="Enter database name [default: aps]: "
if "%db_name%"=="" set db_name=aps

echo.
echo ================================================
echo Updating configuration file...
echo ================================================

REM Use PowerShell to update config file - Only update DATABASE section
powershell -ExecutionPolicy Bypass -Command "try { `$content = Get-Content 'config.ini' -Encoding UTF8; `$inDatabaseSection = `$false; for (`$i = 0; `$i -lt `$content.Length; `$i++) { if (`$content[`$i] -match '^\[DATABASE\]') { `$inDatabaseSection = `$true } elseif (`$content[`$i] -match '^\[.*\]') { `$inDatabaseSection = `$false } elseif (`$inDatabaseSection -and `$content[`$i] -match '^host = ') { `$content[`$i] = 'host = %db_host%' } } `$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Host address updated: %db_host%' } catch { Write-Host 'Failed to update host address' }"

powershell -ExecutionPolicy Bypass -Command "try { `$content = Get-Content 'config.ini' -Encoding UTF8; `$inDatabaseSection = `$false; for (`$i = 0; `$i -lt `$content.Length; `$i++) { if (`$content[`$i] -match '^\[DATABASE\]') { `$inDatabaseSection = `$true } elseif (`$content[`$i] -match '^\[.*\]') { `$inDatabaseSection = `$false } elseif (`$inDatabaseSection -and `$content[`$i] -match '^port = ') { `$content[`$i] = 'port = %db_port%' } } `$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Port updated: %db_port%' } catch { Write-Host 'Failed to update port' }"

powershell -ExecutionPolicy Bypass -Command "try { `$content = Get-Content 'config.ini' -Encoding UTF8; `$inDatabaseSection = `$false; for (`$i = 0; `$i -lt `$content.Length; `$i++) { if (`$content[`$i] -match '^\[DATABASE\]') { `$inDatabaseSection = `$true } elseif (`$content[`$i] -match '^\[.*\]') { `$inDatabaseSection = `$false } elseif (`$inDatabaseSection -and `$content[`$i] -match '^username = ') { `$content[`$i] = 'username = %db_user%' } } `$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Username updated: %db_user%' } catch { Write-Host 'Failed to update username' }"

powershell -ExecutionPolicy Bypass -Command "try { `$content = Get-Content 'config.ini' -Encoding UTF8; `$inDatabaseSection = `$false; for (`$i = 0; `$i -lt `$content.Length; `$i++) { if (`$content[`$i] -match '^\[DATABASE\]') { `$inDatabaseSection = `$true } elseif (`$content[`$i] -match '^\[.*\]') { `$inDatabaseSection = `$false } elseif (`$inDatabaseSection -and `$content[`$i] -match '^password = ') { `$content[`$i] = 'password = %db_password%' } } `$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Password updated' } catch { Write-Host 'Failed to update password' }"

powershell -ExecutionPolicy Bypass -Command "try { `$content = Get-Content 'config.ini' -Encoding UTF8; `$inDatabaseSection = `$false; for (`$i = 0; `$i -lt `$content.Length; `$i++) { if (`$content[`$i] -match '^\[DATABASE\]') { `$inDatabaseSection = `$true } elseif (`$content[`$i] -match '^\[.*\]') { `$inDatabaseSection = `$false } elseif (`$inDatabaseSection -and `$content[`$i] -match '^database = ') { `$content[`$i] = 'database = %db_name%' } } `$content | Set-Content 'config.ini' -Encoding ASCII; Write-Host 'Database name updated: %db_name%' } catch { Write-Host 'Failed to update database name' }"

echo.
echo ================================================
echo Configuration completed!
echo ================================================
echo.
echo Configuration summary:
echo   Database server: %db_host%:%db_port%
echo   Database name: %db_name%
echo   Username: %db_user%
echo.
echo Next steps:
echo 1. Double-click $exeName.exe to start the application
echo 2. Open browser and visit: http://localhost:5000
echo 3. Login with default account: admin / admin
echo.
echo If you encounter problems, please check:
echo - MySQL service is running
echo - Database connection information is correct
echo - Firewall settings allow connections
echo.
echo Press any key to exit...
pause >nul
"@
        
        # 创建PowerShell版本的配置向导
        $psConfigScript = @"
# AEC-FT 数据库配置向导 (PowerShell版本)
# 如果批处理版本无法运行，请使用此脚本

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "   AEC-FT 智能排产指挥平台 - 数据库配置向导" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "此向导将帮助您配置数据库连接信息" -ForegroundColor Yellow
Write-Host ""

# 检查config.ini文件
if (-not (Test-Path "config.ini")) {
    Write-Host "❌ 错误: 找不到config.ini配置文件" -ForegroundColor Red
    Write-Host "请确保此脚本与exe文件在同一目录下" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "请确保MySQL数据库服务已启动" -ForegroundColor Green
Write-Host ""

# 获取数据库配置
`$db_host = Read-Host "请输入MySQL服务器地址 [默认: localhost]"
if ([string]::IsNullOrEmpty(`$db_host)) { `$db_host = "localhost" }

`$db_port = Read-Host "请输入MySQL端口 [默认: 3306]"
if ([string]::IsNullOrEmpty(`$db_port)) { `$db_port = "3306" }

`$db_user = Read-Host "请输入MySQL用户名 [默认: root]"
if ([string]::IsNullOrEmpty(`$db_user)) { `$db_user = "root" }

`$db_password = Read-Host "请输入MySQL密码" -AsSecureString
`$db_password_plain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR(`$db_password))

if ([string]::IsNullOrEmpty(`$db_password_plain)) {
    Write-Host ""
    Write-Host "❌ 错误: MySQL密码不能为空" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

`$db_name = Read-Host "请输入数据库名称 [默认: aps]"
if ([string]::IsNullOrEmpty(`$db_name)) { `$db_name = "aps" }

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "正在更新配置文件..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan

try {
    # 更新配置文件
    `$config = Get-Content "config.ini" -Encoding UTF8
    `$config = `$config -replace "^host = .*", "host = `$db_host"
    `$config = `$config -replace "^port = .*", "port = `$db_port"
    `$config = `$config -replace "^username = .*", "username = `$db_user"
    `$config = `$config -replace "^password = .*", "password = `$db_password_plain"
    `$config = `$config -replace "^database = .*", "database = `$db_name"
    
    `$config | Set-Content "config.ini" -Encoding ASCII
    
    Write-Host "✅ 配置文件更新成功" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 配置文件更新失败: `$_" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Green
Write-Host "配置完成！" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""
Write-Host "配置摘要:" -ForegroundColor Cyan
Write-Host "  数据库服务器: `$db_host:`$db_port" -ForegroundColor White
Write-Host "  数据库名称: `$db_name" -ForegroundColor White
Write-Host "  用户名: `$db_user" -ForegroundColor White
Write-Host ""
Write-Host "接下来的步骤:" -ForegroundColor Cyan
Write-Host "1. 双击运行 $exeName.exe 启动应用" -ForegroundColor White
Write-Host "2. 在浏览器中访问: http://localhost:5000" -ForegroundColor White
Write-Host "3. 使用默认账户登录: admin / admin" -ForegroundColor White
Write-Host ""
Write-Host "如果遇到问题，请检查:" -ForegroundColor Yellow
Write-Host "- MySQL服务是否正在运行" -ForegroundColor White
Write-Host "- 数据库连接信息是否正确" -ForegroundColor White
Write-Host "- 防火墙设置是否允许连接" -ForegroundColor White
Write-Host ""
Read-Host "按回车键退出"
"@

        # 复制诊断工具到dist目录
        if (Test-Path "database_config_diagnostic.py") {
            Copy-Item "database_config_diagnostic.py" "dist/"
            Write-Host "📄 已复制数据库配置诊断工具: dist/database_config_diagnostic.py" -ForegroundColor Green
        }

        # 保存批处理和PowerShell脚本
        $dbInitScript | Out-File -FilePath "dist/database_config_wizard.bat" -Encoding Default
        
        Write-Host "📄 已生成数据库配置向导: dist/database_config_wizard.bat" -ForegroundColor Green
        Write-Host "📄 已生成PowerShell配置向导: dist/数据库配置向导.ps1" -ForegroundColor Green



        # 创建部署说明文件
        $deployInfo = @"
# AEC-FT-智能排产指挥平台 部署指南

## 📋 构建信息
- **版本**: $appVersion
- **构建时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **文件大小**: $sizeMB MB
- **文件名**: $exeName.exe

## 🚀 快速部署步骤

### 1. 准备MySQL数据库
```sql
-- 1. 启动MySQL服务
-- 2. 创建数据库
CREATE DATABASE aps CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 创建用户（可选）
CREATE USER 'aps_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON aps.* TO 'aps_user'@'%';
FLUSH PRIVILEGES;
```

### 2. 配置数据库连接
**方法一：使用配置向导（推荐）**
1. 双击运行 `database_config_wizard.bat`
2. 按提示输入数据库连接信息
3. 向导会自动更新配置文件

**方法二：手动编辑配置文件**
1. 编辑 `config.ini` 文件
2. 修改 [DATABASE] 部分的连接信息：
   ```ini
   [DATABASE]
   host = 你的数据库服务器地址
   port = 3306
   database = aps
   username = 你的数据库用户名
   password = 你的数据库密码
   ```

### 3. 启动应用
1. 双击运行 `$exeName.exe`
2. 等待应用启动完成
3. 在浏览器中访问：http://localhost:5000

### 4. 首次登录
- **用户名**: admin
- **密码**: admin

## 📁 部署包文件说明
- `$exeName.exe` - 主程序文件
- `config.ini` - 数据库配置文件
- `database_config_wizard.bat` - 数据库配置向导（英文版）
- `数据库配置向导.ps1` - PowerShell配置向导（中文版）
- `部署说明-$appVersion.txt` - 本说明文件

## 🔧 常见问题解决

### 问题1: 数据库连接失败
**解决方案:**
1. 检查MySQL服务是否启动
2. 验证数据库连接信息是否正确
3. 确认防火墙设置允许连接
4. 重新运行数据库配置向导

### 问题2: 端口被占用
**解决方案:**
1. 编辑 `config.ini` 文件
2. 修改 [APPLICATION] 部分的端口号
3. 重启应用

### 问题3: 权限不足
**解决方案:**
- 以管理员身份运行程序
- 检查文件夹写入权限

### 问题4: 批处理文件编码问题
**解决方案:**
- 使用英文版本的 `database_config_wizard.bat`
- 或使用PowerShell版本的 `数据库配置向导.ps1`

### 问题5: 日志文件为空或无日志输出
**解决方案:**
1. 检查exe目录下是否有logs文件夹
2. 检查exe启动时的控制台输出
3. 确认应用有写入权限
4. 检查防病毒软件是否阻止文件写入

## 📞 技术支持
如遇到问题，请检查以下日志文件：
- `logs/app.log` - 应用日志（在exe文件同目录的logs文件夹中）
- 控制台输出信息

## 🔧 日志故障排除
1. **检查日志输出**: 查看exe启动时的控制台输出
2. **检查日志目录**: 确认exe目录下存在logs文件夹
3. **查看日志文件**: 应用日志位于 `logs/app.log`
4. **权限问题**: 以管理员身份运行程序

---
**注意**: 生产环境请务必修改默认密码和密钥！
"@
        $deployInfo | Out-File -FilePath "dist/部署说明-$appVersion.txt" -Encoding UTF8
        Write-Host "📄 已生成部署说明文件: dist/部署说明-$appVersion.txt" -ForegroundColor Green
        
    } else {
        Write-Host "❌ 错误: 未找到可执行文件" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "❌ 构建失败" -ForegroundColor Red
    Write-Host "这不应该发生，因为模型验证已通过" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "======================================================" -ForegroundColor Cyan
Write-Host "构建完成" -ForegroundColor Cyan
Write-Host "======================================================" -ForegroundColor Cyan 