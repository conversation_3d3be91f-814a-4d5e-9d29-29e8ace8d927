#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器性能诊断脚本
找出服务器响应慢的根本原因
"""

import requests
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import psutil
import os

def diagnose_server_performance():
    """诊断服务器性能问题"""
    print("🔍 服务器性能诊断")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 1. 测试简单静态文件
    print("📁 测试简单静态文件响应...")
    simple_files = [
        "/static/css/main.css",
        "/static/js/menu-optimizer.js"
    ]
    
    for file_path in simple_files:
        url = f"{base_url}{file_path}"
        start_time = time.time()
        try:
            response = requests.get(url, timeout=10)
            response_time = (time.time() - start_time) * 1000
            print(f"   {file_path}: {response_time:.1f}ms")
            
            if response_time > 1000:
                print(f"   ⚠️ 静态文件响应慢: {response_time:.1f}ms")
        except Exception as e:
            print(f"   ❌ {file_path}: 失败 - {e}")
    
    # 2. 测试数据库连接
    print("\n🗄️ 测试数据库相关API...")
    db_apis = [
        "/api/v2/system/database/info",
        "/api/health"
    ]
    
    session = requests.Session()
    # 先登录
    try:
        session.post(f"{base_url}/auth/login", data={"username": "admin", "password": "admin"})
    except:
        pass
    
    for api_path in db_apis:
        url = f"{base_url}{api_path}"
        start_time = time.time()
        try:
            response = session.get(url, timeout=10)
            response_time = (time.time() - start_time) * 1000
            print(f"   {api_path}: {response_time:.1f}ms (状态: {response.status_code})")
            
            if response_time > 500:
                print(f"   ⚠️ API响应慢: {response_time:.1f}ms")
        except Exception as e:
            print(f"   ❌ {api_path}: 失败 - {e}")
    
    # 3. 测试并发性能
    print("\n🚀 测试并发性能...")
    test_concurrent_requests(base_url)
    
    # 4. 系统资源检查
    print("\n💻 系统资源检查...")
    check_system_resources()
    
    # 5. 分析可能的性能瓶颈
    print("\n🔍 性能瓶颈分析...")
    analyze_performance_bottlenecks()

def test_concurrent_requests(base_url):
    """测试并发请求性能"""
    url = f"{base_url}/static/js/unified_api_client.js"
    
    def make_request():
        start_time = time.time()
        try:
            response = requests.get(url, timeout=10)
            return (time.time() - start_time) * 1000, response.status_code
        except Exception as e:
            return None, str(e)
    
    # 并发5个请求
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(make_request) for _ in range(5)]
        results = [future.result() for future in futures]
    
    successful_times = [result[0] for result in results if result[0] is not None]
    
    if successful_times:
        avg_time = sum(successful_times) / len(successful_times)
        min_time = min(successful_times)
        max_time = max(successful_times)
        
        print(f"   并发请求结果:")
        print(f"   平均响应时间: {avg_time:.1f}ms")
        print(f"   最快响应: {min_time:.1f}ms")
        print(f"   最慢响应: {max_time:.1f}ms")
        print(f"   成功率: {len(successful_times)}/5")
        
        if avg_time > 1000:
            print("   ⚠️ 并发性能差，可能存在线程阻塞")
    else:
        print("   ❌ 所有并发请求都失败了")

def check_system_resources():
    """检查系统资源使用情况"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   CPU使用率: {cpu_percent:.1f}%")
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        print(f"   内存使用率: {memory.percent:.1f}%")
        print(f"   可用内存: {memory.available / 1024 / 1024 / 1024:.1f}GB")
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        if disk_io:
            print(f"   磁盘读取: {disk_io.read_bytes / 1024 / 1024:.1f}MB")
            print(f"   磁盘写入: {disk_io.write_bytes / 1024 / 1024:.1f}MB")
        
        # 网络IO
        net_io = psutil.net_io_counters()
        if net_io:
            print(f"   网络接收: {net_io.bytes_recv / 1024 / 1024:.1f}MB")
            print(f"   网络发送: {net_io.bytes_sent / 1024 / 1024:.1f}MB")
        
        # 检查Python进程
        current_process = psutil.Process()
        print(f"   当前进程CPU: {current_process.cpu_percent():.1f}%")
        print(f"   当前进程内存: {current_process.memory_info().rss / 1024 / 1024:.1f}MB")
        
    except Exception as e:
        print(f"   ⚠️ 无法获取系统资源信息: {e}")

def analyze_performance_bottlenecks():
    """分析性能瓶颈"""
    print("   可能的性能瓶颈:")
    
    # 检查是否在调试模式
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        if 'Werkzeug' in response.headers.get('Server', ''):
            print("   🔴 使用Werkzeug开发服务器 - 这是主要性能瓶颈!")
            print("      建议: 使用Gunicorn或uWSGI生产服务器")
    except:
        pass
    
    # 检查数据库连接
    print("   🔍 数据库连接检查:")
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4'
        )
        
        start_time = time.time()
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        db_time = (time.time() - start_time) * 1000
        
        print(f"      数据库查询时间: {db_time:.1f}ms")
        
        if db_time > 100:
            print("      ⚠️ 数据库响应慢")
        else:
            print("      ✅ 数据库响应正常")
            
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"      ❌ 数据库连接失败: {e}")
    
    # 检查可能的性能问题
    print("   🔍 常见性能问题检查:")
    
    # 检查是否有大量日志输出
    if os.path.exists('logs'):
        log_files = os.listdir('logs')
        total_log_size = 0
        for log_file in log_files:
            log_path = os.path.join('logs', log_file)
            if os.path.isfile(log_path):
                total_log_size += os.path.getsize(log_path)
        
        total_log_mb = total_log_size / 1024 / 1024
        print(f"      日志文件总大小: {total_log_mb:.1f}MB")
        
        if total_log_mb > 100:
            print("      ⚠️ 日志文件过大，可能影响性能")
    
    # 检查是否有性能监控开启
    print("      性能监控: 可能影响响应速度")
    
    print("\n💡 优化建议:")
    print("   1. 🚀 使用生产级WSGI服务器 (Gunicorn/uWSGI)")
    print("   2. 🗄️ 优化数据库查询和连接池")
    print("   3. 📝 减少不必要的日志输出")
    print("   4. 🔧 关闭调试模式和性能监控")
    print("   5. 💾 启用操作系统级别的文件缓存")

def main():
    """主函数"""
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ 服务器正在运行")
    except:
        print("❌ 服务器未运行，请先启动: python run.py")
        return
    
    diagnose_server_performance()
    
    print("\n" + "=" * 50)
    print("🎯 结论:")
    print("如果所有请求都需要2秒左右，问题不在缓存，而在服务器性能。")
    print("最可能的原因是使用了Flask开发服务器而不是生产服务器。")

if __name__ == "__main__":
    main()
