#!/usr/bin/env python3
"""
重启应用以应用缓存修复
"""

import os
import sys
import time
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def restart_flask_app():
    """重启Flask应用"""
    try:
        logger.info("🔄 准备重启Flask应用以应用缓存修复...")
        
        # 检查是否有Flask进程在运行
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True, shell=True)
            if 'python.exe' in result.stdout:
                logger.info("📊 检测到Python进程正在运行")
        except Exception as e:
            logger.warning(f"⚠️ 无法检查进程状态: {e}")
        
        logger.info("💡 请手动重启Flask应用以应用缓存修复:")
        logger.info("   1. 停止当前运行的Flask应用 (Ctrl+C)")
        logger.info("   2. 重新运行: python run.py")
        logger.info("   3. 访问失败批次页面并强制刷新 (Ctrl+F5)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 重启应用失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始应用缓存修复...")
    
    restart_flask_app()
    
    logger.info("=" * 60)
    logger.info("🎉 缓存修复已完成！")
    logger.info("")
    logger.info("📋 接下来的步骤:")
    logger.info("1. 重启Flask应用 (python run.py)")
    logger.info("2. 访问失败批次页面: http://localhost:5000/production/failed-lots")
    logger.info("3. 强制刷新页面 (Ctrl + F5)")
    logger.info("4. 检查控制台是否显示初始化日志")
    logger.info("")
    logger.info("🔍 预期的控制台输出:")
    logger.info("   🔥 失败批次页面初始化...")
    logger.info("   📊 页面URL: http://localhost:5000/production/failed-lots")
    logger.info("   ✅ 所有必要的DOM元素都存在")
    logger.info("   🔍 开始加载失败批次数据...")
    logger.info("   ✅ 成功加载 41 条失败批次记录")

if __name__ == "__main__":
    main()
