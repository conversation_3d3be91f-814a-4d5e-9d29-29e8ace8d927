#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JWH7030QFNAZ产品的数据情况
"""

import pymysql
import json

def check_jwh7030_data():
    """检查JWH7030QFNAZ产品的数据情况"""
    
    # 连接数据库
    connection = pymysql.connect(
        host='localhost',
        user='root', 
        password='WWWwww123!',
        database='aps',
        charset='utf8mb4'
    )

    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询JWH7030QFNAZ产品在eqp_status表中的情况
            print('=== EQP_STATUS表中JWH7030QFNAZ产品设备状态 ===')
            cursor.execute("""
                SELECT HANDLER_ID, DEVICE, STAGE, STATUS, LOT_ID, HANDLER_TYPE, KIT_PN, TB_PN, HB_PN
                FROM eqp_status 
                WHERE DEVICE LIKE %s OR DEVICE LIKE %s
                ORDER BY HANDLER_ID
            """, ('%JWH7030QFNAZ%', '%JWH7030%'))
            
            eqp_results = cursor.fetchall()
            
            if eqp_results:
                for row in eqp_results:
                    print(f"设备: {row['HANDLER_ID']}, 产品: {row['DEVICE']}, 工序: {row['STAGE']}, 状态: {row['STATUS']}")
            else:
                print('未找到JWH7030QFNAZ相关设备状态')
                
            # 查询ET_WAIT_LOT表中JWH7030QFNAZ的待排产批次
            print('\n=== ET_WAIT_LOT表中JWH7030QFNAZ待排产批次 ===')
            cursor.execute("""
                SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, WIP_STATE, PROC_STATE
                FROM et_wait_lot 
                WHERE DEVICE LIKE %s OR DEVICE LIKE %s
                ORDER BY CREATE_TIME
            """, ('%JWH7030QFNAZ%', '%JWH7030%'))
            
            wait_results = cursor.fetchall()
            
            if wait_results:
                for row in wait_results:
                    print(f"批次: {row['LOT_ID']}, 产品: {row['DEVICE']}, 工序: {row['STAGE']}, 数量: {row['GOOD_QTY']}")
            else:
                print('未找到JWH7030QFNAZ相关待排产批次')
                
            # 查询所有JWH7030开头的产品
            print('\n=== 所有JWH7030系列产品 ===')
            cursor.execute("""
                SELECT DISTINCT DEVICE FROM eqp_status WHERE DEVICE LIKE 'JWH7030%'
                UNION
                SELECT DISTINCT DEVICE FROM et_wait_lot WHERE DEVICE LIKE 'JWH7030%'
                ORDER BY DEVICE
            """)
            
            all_products = cursor.fetchall()
            
            for row in all_products:
                print(f"产品: {row['DEVICE']}")
                
            # 查询设备状态统计
            print('\n=== 设备状态统计 ===')
            cursor.execute("""
                SELECT STATUS, COUNT(*) as count
                FROM eqp_status 
                GROUP BY STATUS
                ORDER BY count DESC
            """)
            
            status_stats = cursor.fetchall()
            for row in status_stats:
                print(f"状态: {row['STATUS']}, 数量: {row['count']}")

    finally:
        connection.close()

if __name__ == '__main__':
    check_jwh7030_data()
