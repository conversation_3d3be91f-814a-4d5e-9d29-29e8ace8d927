# Gunicorn配置文件
# 用于生产环境的高性能WSGI服务器配置

import multiprocessing
import os

# 服务器套接字
bind = "127.0.0.1:5000"
backlog = 2048

# 工作进程
workers = multiprocessing.cpu_count() * 2 + 1  # 推荐的工作进程数
worker_class = "sync"  # 同步工作模式
worker_connections = 1000
timeout = 30
keepalive = 2

# 最大请求数（防止内存泄漏）
max_requests = 1000
max_requests_jitter = 50

# 预加载应用（提高性能）
preload_app = True

# 用户和组
# user = "www-data"
# group = "www-data"

# 日志
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程名
proc_name = "aec-ft-platform"

# 临时目录
tmp_upload_dir = None

# SSL（如果需要）
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# 性能调优
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统（Linux）

# 优雅重启
graceful_timeout = 30

# 静态文件处理（建议使用Nginx处理静态文件）
# 这里只是备用方案
def when_ready(server):
    server.log.info("Server is ready. Spawning workers")

def worker_int(worker):
    worker.log.info("worker received INT or QUIT signal")

def pre_fork(server, worker):
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_worker_init(worker):
    worker.log.info("Worker initialized (pid: %s)", worker.pid)

def worker_abort(worker):
    worker.log.info("Worker aborted (pid: %s)", worker.pid)
